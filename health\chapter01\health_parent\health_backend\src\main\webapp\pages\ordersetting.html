<!DOCTYPE html>
<html>
<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>传智健康</title>
    <meta name="description" content="传智健康">
    <meta name="keywords" content="传智健康">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../plugins/elementui/index.css">
    <link rel="stylesheet" href="../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/orderset.css">
    <!-- 引入组件库 -->
    <script src="../js/vue.js"></script>
    <script src="../plugins/elementui/index.js"></script>
    <script type="text/javascript" src="../js/jquery.min.js"></script>
</head>
<body class="hold-transition">
<div id="app">
    <div class="content-header">
        <h1>预约管理<small>预约设置</small></h1>
        <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>预约管理</el-breadcrumb-item>
            <el-breadcrumb-item>预约设置</el-breadcrumb-item>
        </el-breadcrumb>
    </div>
    <div class="app-container">
        <div class="box">
            <div class="box ordersetting">
                <el-card class="box-card">
                    <div class="boxMain">
                        <el-button style="margin-bottom: 20px;margin-right: 20px" type="primary">模板下载</el-button>
                        <el-upload>
                            <el-button type="primary">上传文件</el-button>
                        </el-upload>
                    </div>
                    <div>
                        操作说明：请点击"模板下载"按钮获取模板文件，在模板文件中录入预约设置数据后点击"上传文件"按钮上传模板文件。
                    </div>
                </el-card>
                <div class="calendar">
                    <!-- 年份 月份 -->
                    <div class="month">
                        <div class="currentdate">
                            <span class="choose-year">{{ currentYear }}年</span>
                            <span class="choose-month">{{ currentMonth }}月</span>
                        </div>
                        <div class="choose">
                            <span class="gotoday">今天</span>
                            <span>❮</span>
                            <span>❯</span>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <!-- 星期 -->
                    <div class="caldate">
                        <ul class="weekdays">
                            <li>周一</li>
                            <li>周二</li>
                            <li>周三</li>
                            <li>周四</li>
                            <li>周五</li>
                            <li>周六</li>
                            <li>周日</li>
                        </ul>
                        <!-- 日期 -->
                        <ul class="days">
                            <!-- v-for循环 每一次循环用<li>标签创建一天 -->
                            <li v-for="dayobject in days">
                                <template>
                                    <!-- 非当前月份 -->
                                    <div class="other-month" v-if="dayobject.day.getMonth()+1 != currentMonth">
                                        {{ dayobject.day.getDate() }}
                                    </div>
                                    <!-- 当前月 -->
                                    <div class="everyday" v-if="dayobject.day.getMonth()+1 == currentMonth">
                                        <span class="datenumber">{{ dayobject.day.getDate()}}</span>

                                        <template>
                                            <template v-for="obj in leftobj">
                                                <template v-if="obj.date == dayobject.day.getDate()">
                                                    <template v-if="obj.number > obj.reservations">
                                                        <div class="usual">
                                                            <p>可预约{{obj.number}}人</p>
                                                            <p>已预约{{obj.reservations}}人</p>
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div class="fulled">
                                                            <p>可预约{{obj.number}}人</p>
                                                            <p>已预约{{obj.reservations}}人</p>
                                                            <p>已满</p>
                                                        </div>
                                                    </template>
                                                </template>
                                            </template>
                                            <button v-if="dayobject.day > today" class="orderbtn">设置</button>
                                        </template>
                                    </div>
                                </template>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
<script>
    new Vue({
        el: '#app',
        data:{
            today:new Date(),//当前日期
            currentDay: 1,
            currentMonth: 1,
            LocalMonth: 1,
            currentYear: 1970,
            currentWeek: 1,
            days: [],//当前页对应月份的所有日期
            leftobj: []//用于装载页面显示的月份已经进行预约设置的数据
        },
        created: function () {//在vue初始化时调用
            this.initData(null);//调用日历初始化方法
        },
        methods: {
            //初始化当前页要展示的日期
            initData:function(cur){
                var date;
                var index = 0;   //控制显示预定的天数
                if (cur) {
                    date = new Date(cur);
                } else {
                    var now = new Date();
                    var d = new Date(this.formatDate(now.getFullYear(), now.getMonth(), 1));
                    d.setDate(35);
                    date = new Date(this.formatDate(d.getFullYear(), d.getMonth() + 1, 1));
                }
                this.currentDay = date.getDate();
                this.currentYear = date.getFullYear();
                this.currentMonth = date.getMonth() + 1;
                this.currentWeek = date.getDay(); // //本月第一天是周几（周日0 周六 6）
                var today = new Date();
                this.LocalMonth = today.getMonth() + 1;
                if (this.currentWeek == 0) {
                    this.currentWeek = 7;
                }
                var str = this.formatDate(this.currentYear, this.currentMonth, this.currentDay);
                this.days.length = 0;
                // 今天是周日，放在第一行第7个位置，前面6个
                //初始化本周
                for (var i = this.currentWeek - 1; i >= 0; i--) {
                    var d = new Date(str);
                    d.setDate(d.getDate() - i);
                    var dayobject = {};
                    dayobject.day = d;
                    var now = new Date();
                    if (d.getDate() === (now.getDate()) && d.getMonth() === now.getMonth() && d.getFullYear() === now.getFullYear()) {
                        dayobject.index = index++;//从今天开始显示供预定的数量
                    }
                    else if (index != 0 && index < 3)
                        dayobject.index = index++;//从今天开始3天内显示供预定的数量
                    this.days.push(dayobject);//将日期放入data 中的days数组 供页面渲染使用
                }
                //其他周
                for (var i = 1; i <= 35 - this.currentWeek; i++) {
                    var d = new Date(str);
                    d.setDate(d.getDate() + i);
                    var dayobject = {};//dayobject   {day:date,index:2}
                    dayobject.day = d;
                    var now = new Date();
                    if (d.getDate() === (now.getDate()) && d.getMonth() === now.getMonth() && d.getFullYear() === now.getFullYear()) {
                        dayobject.index = index++;
                    }
                    else if (index != 0 && index < 3)
                        dayobject.index = index++;
                    this.days.push(dayobject);
                }
            },
            // 返回 类似 2016-01-02 格式的字符串
            formatDate: function (year, month, day) {
                var y = year;
                var m = month;
                if (m < 10) m = "0" + m;
                var d = day;
                if (d < 10) d = "0" + d;
                return y + "-" + m + "-" + d
            },
        }
    })
</script>
</html>