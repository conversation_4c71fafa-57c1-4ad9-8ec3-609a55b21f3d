<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="health_backend" />
        <module name="health_service_provider" />
        <module name="health_common" />
        <module name="health_mobile" />
        <module name="health_interface" />
      </profile>
    </annotationProcessing>
  </component>
</project>