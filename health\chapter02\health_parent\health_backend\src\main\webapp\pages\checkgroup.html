<!DOCTYPE html>
<html>
<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>传智健康</title>
    <meta name="description" content="传智健康">
    <meta name="keywords" content="传智健康">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../plugins/elementui/index.css">
    <link rel="stylesheet" href="../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <!-- 引入组件库 -->
    <script type="text/javascript" src="../js/jquery.min.js"></script>
    <script src="../js/vue.js"></script>
    <script src="../plugins/elementui/index.js"></script>
    <style>
        .datatable {
            position: relative;
            box-sizing: border-box;
            -webkit-box-flex: 1;
            width: 100%;
            max-width: 100%;
            font-size: 14px;
            color: rgb(96, 98, 102);
            overflow: hidden;
            flex: 1 1 0%;
        }
        .datatable td, .datatable th {
            padding: 12px 0;
            min-width: 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            text-overflow: ellipsis;
            vertical-align: middle;
            position: relative;
            text-align: left;
        }
    </style>
</head>
<body class="hold-transition">
    <div id="app">
        <div class="content-header">
            <h1>预约管理<small>检查组管理</small></h1>
            <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumb">
                <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                <el-breadcrumb-item>预约管理</el-breadcrumb-item>
                <el-breadcrumb-item>检查组管理</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="app-container">
            <div class="box">
                <div class="filter-container">
                    <el-input placeholder="编码/名称/助记码" v-model="pagination.queryString" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter"></el-input>
                    <el-button class="dalfBut">查询</el-button>
                    <el-button type="primary" class="butT">新增</el-button>
                </div>
                <el-table size="small" current-row-key="id" :data="dataList" stripe highlight-current-row>
                    <el-table-column type="index" align="center" label="序号"></el-table-column>
                    <el-table-column prop="code" label="检查组编码" align="center"></el-table-column>
                    <el-table-column prop="name" label="检查组名称" align="center"></el-table-column>
                    <el-table-column label="适用性别" align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.sex == '0' ? '不限' : scope.row.sex == '1' ? '男' : '女'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="helpCode" label="助记码" align="center"></el-table-column>
                    <el-table-column prop="remark" label="说明" align="center"></el-table-column>
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button type="primary" size="mini">编辑</el-button>
                            <el-button size="mini" type="danger">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination-container">
                    <el-pagination
                        class="pagiantion"
                        layout="total, prev, pager, next, jumper">
                    </el-pagination>
                </div>
                <!-- 新增标签弹层 -->
                <div class="add-form">
                    <el-dialog title="新增检查组" :visible.sync="dialogFormVisible">
                        <template>
                            <el-tabs v-model="activeName" type="card">
                                <el-tab-pane label="基本信息" name="first">
                                    <el-form label-position="right" label-width="100px">
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="编码">
                                                    <el-input v-model="formData.code"/>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="名称">
                                                    <el-input v-model="formData.name"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="适用性别">
                                                    <el-select v-model="formData.sex">
                                                        <el-option label="不限" value="0"></el-option>
                                                        <el-option label="男" value="1"></el-option>
                                                        <el-option label="女" value="2"></el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="助记码">
                                                    <el-input v-model="formData.helpCode"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="说明">
                                                    <el-input v-model="formData.remark" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="注意事项">
                                                    <el-input v-model="formData.attention" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </el-tab-pane>
                                <el-tab-pane label="检查项信息" name="second">
                                <div class="checkScrol">
                                    <table class="datatable">
                                        <thead>
                                        <tr>
                                            <th>选择</th>
                                            <th>项目编码</th>
                                            <th>项目名称</th>
                                            <th>项目说明</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="c in tableData">
                                            <td>
                                                <input :id="c.id" v-model="checkitemIds" type="checkbox" :value="c.id">
                                            </td>
                                            <td><label :for="c.id">{{c.code}}</label></td>
                                            <td><label :for="c.id">{{c.name}}</label></td>
                                            <td><label :for="c.id">{{c.remark}}</label></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                </el-tab-pane>
                            </el-tabs>
                        </template>
                        <div slot="footer" class="dialog-footer">
                            <el-button>取消</el-button>
                            <el-button type="primary">确定</el-button>
                        </div>
                    </el-dialog>
                </div>

                <!-- 编辑标签弹层 -->
                <div class="edit-form">
                    <el-dialog title="编辑检查组" :visible.sync="dialogFormVisible4Edit">
                        <template>
                            <el-tabs v-model="activeName" type="card">
                                <el-tab-pane label="基本信息" name="first">
                                    <el-form label-position="right" label-width="100px">
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="编码">
                                                    <el-input v-model="formData.code"/>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="名称">
                                                    <el-input v-model="formData.name"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="适用性别">
                                                    <el-select v-model="formData.sex">
                                                        <el-option label="不限" value="0"></el-option>
                                                        <el-option label="男" value="1"></el-option>
                                                        <el-option label="女" value="2"></el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="助记码">
                                                    <el-input v-model="formData.helpCode"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="说明">
                                                    <el-input v-model="formData.remark" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="注意事项">
                                                    <el-input v-model="formData.attention" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </el-tab-pane>
                                <el-tab-pane label="检查项信息" name="second">
                                    <div class="checkScrol">
                                        <table class="datatable">
                                            <thead>
                                                <tr>
                                                    <th>选择</th>
                                                    <th>项目编码</th>
                                                    <th>项目名称</th>
                                                    <th>项目说明</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="c in tableData">
                                                    <td>
                                                        <input :id="c.id" v-model="checkitemIds" type="checkbox" :value="c.id">
                                                    </td>
                                                    <td><label :for="c.id">{{c.code}}</label></td>
                                                    <td><label :for="c.id">{{c.name}}</label></td>
                                                    <td><label :for="c.id">{{c.remark}}</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </el-tab-pane>
                            </el-tabs>
                        </template>
                        <div slot="footer" class="dialog-footer">
                            <el-button>取消</el-button>
                            <el-button type="primary">确定</el-button>
                        </div>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>
</body>

<script>
    var vue = new Vue({
        el: '#app',
        data:{
            activeName:'first',//添加/编辑窗口Tab标签名称
            pagination: {//分页相关属性
            },
            dataList: [],//列表数据
            formData: {},//表单数据
            tableData:[],//新增和编辑表单中对应的检查项列表数据
            checkitemIds:[],//新增和编辑表单中检查项对应的复选框，基于双向绑定可以进行回显和数据提交
            dialogFormVisible: false,//控制添加窗口显示/隐藏
            dialogFormVisible4Edit:false//控制编辑窗口显示/隐藏
        },
        //钩子函数，VUE对象初始化完成后自动执行
        created() {
        },
        methods:{
        }
    })
</script>
</html>
