<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,user-scalable=no,minimal-ui">
        <meta name="description" content="">
        <meta name="author" content="">
        <link rel="icon" href="../img/asset-favico.ico">
        <title>传智健康</title>
        <link rel="stylesheet" href="../css/page-health-index.css" />
        <link rel="stylesheet" href="../css/page-health-login.css" />
        <link rel="stylesheet" href="../plugins/elementui/index.css" />
        <script src="../plugins/jquery/dist/jquery.min.js"></script>
        <script src="../plugins/healthmobile.js"></script>
        <script src="../plugins/vue/vue.js"></script>
        <script src="../plugins/vue/axios-0.18.0.js"></script>
        <script src="../plugins/elementui/index.js"></script>
    </head>
    <body data-spy="scroll" data-target="#myNavbar" data-offset="150">
        <div class="app">
            <!-- 页面头部 -->
            <div class="top-header">
                <span class="f-left"><i class="icon-back" onclick="history.go(-1)"></i></span>
                <span class="center">传智健康</span>
                <span class="f-right"><i class="icon-more"></i></span>
            </div>
            <!-- 页面内容 -->
            <div class="contentBox">
                <div class="list-column2">
                    <ul class="list">
                        <li class="type-item" style="width: 100%">
                            <a class="link-page">
                                <div class="type-title">
                                    <h3>张三</h3>
                                    <p>男</p>
                                    <p>入职体检套餐</p>
                                    <p>2019-09-08</p>
                                </div>
                                <div class="type-icon">
                                    查看
                                </div>
                            </a>
                        </li>
                    </ul>
                    <ul class="list">
                        <li class="type-item" style="width: 100%">
                            <a class="link-page">
                                <div class="type-title">
                                    <h3>张三</h3>
                                    <p>男</p>
                                    <p>入职体检套餐</p>
                                    <p>2019-09-08</p>
                                </div>
                                <div class="type-icon">
                                    查看
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
</html>