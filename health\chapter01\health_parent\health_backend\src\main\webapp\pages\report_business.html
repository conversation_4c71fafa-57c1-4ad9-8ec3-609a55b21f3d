<!DOCTYPE html>
<html>
<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>传智健康</title>
    <meta name="description" content="传智健康">
    <meta name="keywords" content="传智健康">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../plugins/elementui/index.css">
    <link rel="stylesheet" href="../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .grid-content {
            border-radius: 4px;
            min-height: 40px;
        }
    </style>
</head>
<body class="hold-transition">
<div id="app">
    <div class="content-header">
        <h1>统计分析<small>运营数据</small></h1>
        <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumb">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>统计分析</el-breadcrumb-item>
            <el-breadcrumb-item>运营数据</el-breadcrumb-item>
        </el-breadcrumb>
    </div>
    <div class="app-container">
        <div class="box" style="height: 900px">
            <div class="excelTitle">
                <el-button>导出Excel</el-button>
                <el-button>导出PDF</el-button>
                运营数据统计
            </div>
            <div class="excelTime">日期：{{reportData.reportDate}}</div>
            <table class="exceTable" cellspacing="0" cellpadding="0">
                <tr>
                    <td colspan="4" class="headBody">会员数据统计</td>
                </tr>
                <tr>
                    <td width='20%' class="tabletrBg">新增会员数</td>
                    <td width='30%'>{{reportData.todayNewMember}}</td>
                    <td width='20%' class="tabletrBg">总会员数</td>
                    <td width='30%'>{{reportData.totalMember}}</td>
                </tr>
                <tr>
                    <td class="tabletrBg">本周新增会员数</td>
                    <td>{{reportData.thisWeekNewMember}}</td>
                    <td class="tabletrBg">本月新增会员数</td>
                    <td>{{reportData.thisMonthNewMember}}</td>
                </tr>
                <tr>
                    <td colspan="4" class="headBody">预约到诊数据统计</td>
                </tr>
                <tr>
                    <td class="tabletrBg">今日预约数</td>
                    <td>{{reportData.todayOrderNumber}}</td>
                    <td class="tabletrBg">今日到诊数</td>
                    <td>{{reportData.todayVisitsNumber}}</td>
                </tr>
                <tr>
                    <td class="tabletrBg">本周预约数</td>
                    <td>{{reportData.thisWeekOrderNumber}}</td>
                    <td class="tabletrBg">本周到诊数</td>
                    <td>{{reportData.thisWeekVisitsNumber}}</td>
                </tr>
                <tr>
                    <td class="tabletrBg">本月预约数</td>
                    <td>{{reportData.thisMonthOrderNumber}}</td>
                    <td class="tabletrBg">本月到诊数</td>
                    <td>{{reportData.thisMonthVisitsNumber}}</td>
                </tr>
                <tr>
                    <td colspan="4" class="headBody">热门套餐</td>
                </tr>
                <tr class="tabletrBg textCenter">
                    <td>套餐名称</td>
                    <td>预约数量</td>
                    <td>占比</td>
                    <td>备注</td>
                </tr>
                <tr v-for="s in reportData.hotSetmeal">
                    <td>{{s.name}}</td>
                    <td>{{s.setmeal_count}}</td>
                    <td>{{s.proportion}}</td>
                    <td></td>
                </tr>
            </table>
        </div>
    </div>
</div>
</body>
<!-- 引入组件库 -->
<script src="../js/vue.js"></script>
<script src="../plugins/elementui/index.js"></script>
<script type="text/javascript" src="../js/jquery.min.js"></script>
<script>
    var vue = new Vue({
        el: '#app',
        data: {
            reportData: {
                reportDate: null,            //日期
                todayNewMember: 0,          //新增会员数
                totalMember: 0,             //总会员数
                thisWeekNewMember: 0,       //本周新增会员数
                thisMonthNewMember: 0,      //本月新增会员数
                todayOrderNumber: 0,        //今日预约数
                todayVisitsNumber: 0,       //今日到诊数
                thisWeekOrderNumber: 0,     //本周预约数
                thisWeekVisitsNumber: 0,    //本周到诊数
                thisMonthOrderNumber: 0,    //本月预约数
                thisMonthVisitsNumber: 0,   //本月到诊数
                hotSetmeal: []              //热门套餐
            }
        }
    })
</script>
</html>
