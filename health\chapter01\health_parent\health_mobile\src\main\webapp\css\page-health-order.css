/*
 * colors variables
 */
/*
 * colors variables
 */
html, body {
  min-height: 100%;
  height: 100%;
  background: #f3f2f7;
  line-height: 2;
  font-family: Helvetica;
}

html, body, h1, h2, h3, h4, h5, h6, p, ul, li {
  margin: 0;
  padding: 0;
}

ul, li {
  list-style: none;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: normal;
}

h3 {
  font-size: 16px;
}

h4 {
  font-size: 14px;
}

p {
  font-size: 12px;
}

em, i {
  font-style: normal;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

a:hover {
  -webkit-tap-highlight-color: transparent;
}

.link-page {
  display: block;
}

img {
  width: 100%;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.center {
  text-align: center;
}

.img-parent {
  overflow: hidden;
}

:focus {
  outline: none;
}

input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #d1d1d6;
}

input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #d1d1d6;
}

input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #d1d1d6;
}

input::-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #d1d1d6;
}

@font-face {
  font-family: 'health-icon';
  src: url("../fonts/health-icon.eot?l7yt0w");
  src: url("fonts/health-icon.eot?l7yt0w#iefix") format("embedded-opentype"), url("../fonts/health-icon.ttf?l7yt0w") format("truetype"), url("../fonts/health-icon.woff?l7yt0w") format("woff"), url("../fonts/health-icon.svg?l7yt0w") format("svg");
  font-weight: normal;
  font-style: normal;
}

i {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'health-icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-left-arrow:before {
  content: "\e940";
  color: #515151;
}

.icon-rit-arrow2:before {
  content: "\e941";
  color: #515151;
}

.icon-case .path1:before {
  content: "\e91a";
  color: #48bcf5;
}

.icon-case .path2:before {
  content: "\e91b";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path3:before {
  content: "\e91c";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path4:before {
  content: "\e91d";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path5:before {
  content: "\e91e";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path6:before {
  content: "\e91f";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path7:before {
  content: "\e920";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path8:before {
  content: "\e921";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path9:before {
  content: "\e922";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path10:before {
  content: "\e923";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path11:before {
  content: "\e924";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path12:before {
  content: "\e925";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path1:before {
  content: "\e926";
  color: #48bcf5;
}

.icon-allergy .path2:before {
  content: "\e927";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path3:before {
  content: "\e928";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path4:before {
  content: "\e929";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path5:before {
  content: "\e92a";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path6:before {
  content: "\e92b";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path7:before {
  content: "\e92c";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path8:before {
  content: "\e92d";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path9:before {
  content: "\e92e";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path10:before {
  content: "\e92f";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path11:before {
  content: "\e930";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path12:before {
  content: "\e931";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path13:before {
  content: "\e932";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path14:before {
  content: "\e933";
  margin-left: -1em;
  color: #407afd;
}

.icon-baseinfo .path1:before {
  content: "\e934";
  color: #48bcf5;
}

.icon-baseinfo .path2:before {
  content: "\e935";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path1:before {
  content: "\e936";
  color: #48bcf5;
}

.icon-family .path2:before {
  content: "\e937";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path3:before {
  content: "\e938";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path4:before {
  content: "\e939";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path5:before {
  content: "\e93a";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path6:before {
  content: "\e93b";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path7:before {
  content: "\e93c";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path8:before {
  content: "\e93d";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path9:before {
  content: "\e93e";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path10:before {
  content: "\e93f";
  margin-left: -1em;
  color: #407afd;
}

.icon-rit-arrow:before {
  content: "\e919";
}

.icon-back:before {
  content: "\e900";
  color: #fff;
}

.icon-more:before {
  content: "\e901";
  color: #fff;
}

.icon-zhen .path1:before {
  content: "\e902";
  color: #48bcf5;
}

.icon-zhen .path2:before {
  content: "\e904";
  margin-left: -1em;
  color: #4076fe;
}

.icon-search .path1:before {
  content: "\e903";
  color: #48bcf5;
}

.icon-search .path2:before {
  content: "\e905";
  margin-left: -1em;
  color: #4076fe;
}

.icon-search .path3:before {
  content: "\e917";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-search .path4:before {
  content: "\e918";
  margin-left: -1em;
  color: #4076fe;
}

.icon-record .path1:before {
  content: "\e906";
  color: #48bcf5;
}

.icon-record .path2:before {
  content: "\e907";
  margin-left: -1em;
  color: #4076fe;
}

.icon-record .path3:before {
  content: "\e908";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-record .path4:before {
  content: "\e909";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-record .path5:before {
  content: "\e90a";
  margin-left: -1em;
  color: #4076fe;
}

.icon-star .path1:before {
  content: "\e90b";
  color: #48bcf5;
}

.icon-star .path2:before {
  content: "\e90c";
  margin-left: -1em;
  color: #4076fe;
}

.icon-plus .path1:before {
  content: "\e90d";
  color: #48bcf5;
}

.icon-plus .path2:before {
  content: "\e90e";
  margin-left: -1em;
  color: #4076fd;
}

.icon-person .path1:before {
  content: "\e90f";
  color: #48bcf5;
}

.icon-person .path2:before {
  content: "\e910";
  margin-left: -1em;
  color: #4076fe;
}

.icon-date:before {
  content: "\e911";
  color: #4b88fc;
}

.icon-data:before {
  content: "\e912";
  color: #fff;
}

.icon-ask-cur:before {
  content: "\e913";
  color: #fff;
}

.icon-ask-circle .path1:before {
  content: "\e914";
  color: #48bcf5;
}

.icon-ask-circle .path2:before {
  content: "\e915";
  margin-left: -1em;
  color: #4076fe;
}

.icon-tizhi:before {
  content: "\e916";
  color: #fff;
}

.app {
  max-width: 750px;
  margin: 0 auto;
}

/*
 * colors variables
 */
.top-header {
  background-color: #000;
  color: #fff;
  text-align: center;
  height: 45px;
  line-height: 45px;
  font-size: 16px;
  padding: 0 10px;
}

/*
 * colors variables
 */
.select-drop {
  position: relative;
  border-bottom: 1px solid #eee;
  padding: 1% 3%;
  background-color: #fff;
}

.select-drop label {
  padding-right: 10px;
  border-right: 1px solid #e3e3e3;
}

.select-drop .filter-box {
  position: relative;
  display: inline-block;
}

.select-drop .filter-box .filter-text {
  position: relative;
  width: 14%;
  cursor: pointer;
  padding: 0 30px 0 10px;
  background-color: transparent;
}

.select-drop .filter-box .filter-text input {
  font-size: 15px;
}

.select-drop .filter-box .filter-text .filter-title {
  height: 36px;
  line-height: 36px;
  border: 0;
  background-color: transparent;
  width: 100%;
  text-align: center;
}

.select-drop .filter-box .filter-text .icon {
  position: absolute;
}

.select-drop .filter-box .filter-text .icon.icon-filter-arrow {
  width: 8px;
  height: 6px;
  background-repeat: no-repeat;
  background-image: url(../img/widget-icon_arrow.png);
  background-size: 100%;
  right: 10px;
  top: 15px;
  transition: all .2s;
}

.select-drop .filter-box .filter-text .icon.icon-filter-arrow.filter-show {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.select-drop .filter-box select {
  display: none;
}

.select-drop .filter-box .filter-list {
  display: none;
  width: 30%;
  max-height: 300px;
  background: #fff;
  font-size: 15px;
  position: absolute;
  top: 42px;
  left: 0;
  z-index: 99;
  border: 1px solid #e3e3e3;
  overflow: auto;
}

.select-drop .filter-box .filter-list li a {
  display: block;
  padding: 0 10px;
  line-height: 36px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
}

.select-drop .filter-box .filter-list li.filter-selected {
  background-color: #26acff;
}

.select-drop .filter-box .filter-list li.filter-selected a {
  display: block;
  color: #fff;
  line-height: 28px;
}

.select-drop .filter-box .filter-list li.filter-disabled:hover a {
  cursor: not-allowed !important;
  background-color: #fff;
}

.select-drop .filter-box .filter-list li.filter-disabled a {
  display: block;
  color: #d2d2d2;
}

.select-drop .filter-box .filter-list li:hover {
  background-color: #f2f2f2;
}

.select-drop .filter-box .filter-list li.filter-null a {
  color: #d2d2d2;
}

/*
 * colors variables
 */
.list-column1 .list {
  padding: 0 5.3%;
  overflow: hidden;
  background-color: #fff;
}

.list-column1 .list .list-item {
  padding: 5.3% 0;
  border-bottom: 1px solid #e3e3e3;
}

.list-column1 .list .list-item a {
  display: flex;
  align-items: center;
}

.list-column1 .list .list-item .img-object {
  max-width: 28%;
  height: auto;
  margin-right: 4.6%;
}

.list-column1 .list .list-item .item-body {
  overflow: hidden;
}

.list-column1 .list .list-item .item-body .ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.list-column1 .list .list-item .item-body .ellipsis-more {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
}

.list-column1 .list .list-item .item-body .item-title {
  margin-top: 0;
  font-weight: normal;
  color: #333;
}

.list-column1 .list .list-item .item-body .item-desc {
  line-height: 1.5;
  color: #999;
}

.list-column1 .list .list-item .item-body .item-keywords {
  margin-top: 2%;
  overflow: hidden;
}

.list-column1 .list .list-item .item-body .item-keywords span {
  padding: 2px 8px;
  color: #ea5504;
  margin-right: 3.3%;
  border: 1px solid #ffb891;
  border-radius: 2px;
}

.list-column1 .list .list-item .item-body .item-link {
  text-align: right;
  margin-top: -1.3%;
  overflow: hidden;
}

.list-column1 .list .list-item .item-body .item-link span {
  background-image: linear-gradient(90deg, #24aafd, #0088dc);
  color: #fff;
  padding: 2% 5.9%;
  border-radius: 5px;
  font-size: 13px;
}
