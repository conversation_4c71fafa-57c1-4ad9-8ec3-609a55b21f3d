﻿<!DOCTYPE html>
<html>
<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>传智健康</title>
    <meta name="description" content="传智健康">
    <meta name="keywords" content="传智健康">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../plugins/elementui/index.css">
    <link rel="stylesheet" href="../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style type="text/css">
    .el-main{
        position: absolute;
        top: 70px;
        bottom: 0px;
        left: 200px;
        right: 10px;
        padding: 0;
    }
    </style>
</head>
<body class="hold-transition skin-purple sidebar-mini">
    <div id="app">
        <el-container>
            <el-header  class="main-header" style="height:70px;">
                <nav class="navbar navbar-static-top" :class=''>
                    <!-- Logo -->
                    <a href="#" class="logo" style="text-align:center">
                        <span class="logo-lg"><img src="../img/logo.png"></span>
                    </a>
                    <div class="right-menu">
                        <span class="help"><i class="fa fa-exclamation-circle" aria-hidden="true"></i>帮助</span>
                        <el-dropdown class="avatar-container right-menu-item" trigger="click">
                            <div class="avatar-wrapper">
                                <img src="../img/user2-160x160.jpg" class="user-avatar">
                                {{username}}
                            </div>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item divided>
                                    <span style="display:block;">退出</span>
                                </el-dropdown-item>
<!--                                <el-dropdown-item divided>-->
<!--                                    <span style="display:block;">修改密码</span>-->
<!--                                </el-dropdown-item>-->
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </nav>
            </el-header>
            <el-container>
                <el-aside width="200px">
                    <el-menu>
                        <el-submenu v-for="menu in menuList" :index="menu.path">
                            <template slot="title">
                                <i class="fa" :class="menu.icon"></i>
                                {{menu.title}}
                            </template>
                            <template v-for="child in menu.children">
                                <el-menu-item :index="child.path">
                                    <a :href="child.linkUrl" target="right">{{child.title}}</a>
                                </el-menu-item>
                            </template>
                        </el-submenu>
                    </el-menu>
                </el-aside>
                <el-container>
                    <iframe name="right" class="el-main" src="ordersetting.html" width="100%" height="580px" frameborder="0"></iframe>
                </el-container>
            </el-container>
        </el-container>
    </div>
</body>
<!-- 引入组件库 -->
<script src="../js/vue.js"></script>
<script src="../plugins/elementui/index.js"></script>
<script type="text/javascript" src="../js/jquery.min.js"></script>
<script>
    new Vue({
        el: '#app',
        data:{
            username:null,
            menuList:[
                // {
                //     "path": "1",
                //     "title": "工作台",
                //     "icon":"fa-dashboard",
                //     "children": []
                // },
                // {
                //     "path": "2",
                //     "title": "会员管理",
                //     "icon":"fa-user-md",
                //     "children": [
                //         {
                //             "path": "/2-1",
                //             "title": "会员档案",
                //             "linkUrl":"member.html",
                //             "children":[]
                //         },
                //         {
                //             "path": "/2-2",
                //             "title": "体检上传",
                //             "children":[]
                //         },
                //         {
                //             "path": "/2-3",
                //             "title": "会员统计",
                //             "linkUrl":"all-item-list.html",
                //             "children":[]
                //         },
                //     ]
                // },
                {
                    "path": "3",
                    "title": "预约管理",
                    "icon":"fa-tty",
                    "children": [
                        // {
                        //     "path": "/3-1",
                        //     "title": "预约列表",
                        //     "linkUrl":"ordersettinglist.html",
                        //     "children":[]
                        // },
                        {
                            "path": "/3-2",
                            "title": "预约设置",
                            "linkUrl":"ordersetting.html",
                            "children":[]
                        },
                        {
                            "path": "/3-3",
                            "title": "套餐管理",
                            "linkUrl":"setmeal.html",
                            "children":[]
                        },
                        {
                            "path": "/3-4",
                            "title": "检查组管理",
                            "linkUrl":"checkgroup.html",
                            "children":[]
                        },
                        {
                            "path": "/3-5",
                            "title": "检查项管理",
                            "linkUrl":"checkitem.html",
                            "children":[]
                        },
                    ]
                },
                // {
                //     "path": "4",
                //     "title": "健康评估",
                //     "icon":"fa-stethoscope",
                //     "children":[
                //         {
                //             "path": "/4-1",
                //             "title": "中医体质辨识",
                //             "linkUrl":"all-medical-list.html",
                //             "children":[]
                //         },
                //     ]
                // },
                {
                    "path": "5",     //菜单项所对应的路由路径
                    "title": "统计分析",     //菜单项名称
                    "icon":"fa-heartbeat",
                    "children":[//是否有子菜单，若没有，则为[]
                        {
                            "path": "/5-1",
                            "title": "会员数量统计",
                            "linkUrl":"report_member.html",
                            "children":[]
                        },
                        {
                            "path": "/5-2",
                            "title": "预约套餐占比统计",
                            "linkUrl":"report_setmeal.html",
                            "children":[]
                        },
                        {
                            "path": "/5-3",
                            "title": "运营数据统计",
                            "linkUrl":"report_business.html",
                            "children":[]
                        }
                    ]
                }
            ]
        },
    })
    $(function() {
            var wd = 200;
            $(".el-main").css('width', $('body').width() - wd + 'px');
    });
</script>
</html>
