-- 快速数据库设置
CREATE DATABASE IF NOT EXISTS health DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE health;

-- 检查项表
CREATE TABLE IF NOT EXISTS t_checkitem (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(16) UNIQUE NOT NULL,
    name VARCHAR(32) NOT NULL,
    sex CHAR(1),
    age VARCHAR(32),
    price FLOAT,
    type CHAR(1),
    remark VARCHAR(128),
    attention VARCHAR(128)
);

-- 检查组表
CREATE TABLE IF NOT EXISTS t_checkgroup (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VA<PERSON>HAR(32) UNIQUE NOT NULL,
    name VARCHAR(32) NOT NULL,
    helpCode VARCHAR(32),
    sex CHAR(1),
    remark VARCHAR(128),
    attention VARCHAR(128)
);

-- 套餐表
CREATE TABLE IF NOT EXISTS t_setmeal (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(128) NOT NULL,
    code VARCHAR(8) UNIQUE NOT NULL,
    helpCode VARCHAR(32),
    sex CHAR(1),
    age VARCHAR(32),
    price FLOAT,
    remark VARCHAR(200),
    attention VARCHAR(200),
    img VARCHAR(128)
);

-- 关联表
CREATE TABLE IF NOT EXISTS t_checkgroup_checkitem (
    checkgroup_id INT NOT NULL,
    checkitem_id INT NOT NULL,
    PRIMARY KEY (checkgroup_id, checkitem_id)
);

CREATE TABLE IF NOT EXISTS t_setmeal_checkgroup (
    setmeal_id INT NOT NULL,
    checkgroup_id INT NOT NULL,
    PRIMARY KEY (setmeal_id, checkgroup_id)
);

-- 用户表
CREATE TABLE IF NOT EXISTS t_user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    birthday DATE,
    gender VARCHAR(32),
    username VARCHAR(32) UNIQUE NOT NULL,
    password VARCHAR(256) NOT NULL,
    remark VARCHAR(32),
    station VARCHAR(128),
    telephone VARCHAR(32)
);

-- 插入测试数据
INSERT IGNORE INTO t_user (username, password, remark) VALUES ('admin', 'admin', '管理员');

INSERT IGNORE INTO t_checkitem (code, name, sex, age, price, type, remark, attention) VALUES 
('0001', '身高', '0', '0-100', 5.0, '1', '身高测量', '请脱鞋'),
('0002', '体重', '0', '0-100', 5.0, '1', '体重测量', '请空腹'),
('0003', '血压', '0', '18-100', 20.0, '1', '血压检查', '休息5分钟'),
('0004', '血常规', '0', '0-100', 25.0, '2', '血液检查', '空腹8小时');

INSERT IGNORE INTO t_checkgroup (code, name, helpCode, sex, remark, attention) VALUES 
('0001', '一般检查', 'YBJC', '0', '基础检查', '按时到达'),
('0002', '血液检查', 'XYJC', '0', '血液相关', '需要空腹');

INSERT IGNORE INTO t_checkgroup_checkitem (checkgroup_id, checkitem_id) VALUES 
(1, 1), (1, 2), (1, 3), (2, 4);

SELECT 'Database setup completed!' AS status;
