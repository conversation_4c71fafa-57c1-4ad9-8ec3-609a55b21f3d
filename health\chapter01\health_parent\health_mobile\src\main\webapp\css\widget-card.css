/*
 * colors variables
 */
.card {
  background-color: #fff;
}

.card i {
  font-size: 16px;
  vertical-align: middle;
}

.card .project-text {
  padding: 3.3%;
}

.card .project-text p {
  margin-bottom: 10px;
}

.card .project-text .tit {
  font-weight: normal;
  font-size: 16px;
  color: #333;
  line-height: 1.2;
}

.card .project-text .subtit {
  font-size: 14px;
  color: #818181;
  line-height: 2;
}

.card .project-text .keywords {
  font-size: 12px;
  color: #ea5504;
}

.card .project-text .keywords span {
  padding: 5px 2%;
  border: 1px solid #ffb891;
  border-radius: 6px;
  margin-right: 1.3%;
}

.card .project-know {
  border-top: 1px solid #e3e3e3;
  padding: 0 3.3%;
  font-size: 14px;
  line-height: 3;
  position: relative;
}

.card .project-know .word {
  color: #666;
}

.card .project-know .arrow {
  float: right;
}

.card .project-know .arrow .icon-rit-arrow {
  font-weight: bold;
}
