<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,user-scalable=no,minimal-ui">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../img/asset-favico.ico">
    <title>预约</title>
    <link rel="stylesheet" href="../css/page-health-order.css"/>
</head>
<body data-spy="scroll" data-target="#myNavbar" data-offset="150">
<div class="app" id="app">
    <!-- 页面头部 -->
    <div class="top-header">
        <span class="f-left"><i class="icon-back" onclick="history.go(-1)"></i></span>
        <span class="center">传智健康</span>
        <span class="f-right"><i class="icon-more"></i></span>
    </div>
    <!-- 页面内容 -->
    <div class="contentBox">
        <div class="list-column1">
            <ul class="list">
                <li class="list-item" v-for="setmeal in setmealList">
                    <a class="link-page" :href="'setmeal_detail.html?id='+setmeal.id">
                        <img class="img-object f-left" :src="'http://puco9aur6.bkt.clouddn.com/'+setmeal.img" alt="">
                        <div class="item-body">
                            <h4 class="ellipsis item-title">{{setmeal.name}}</h4>
                            <p class="ellipsis-more item-desc">{{setmeal.remark}}</p>
                            <p class="item-keywords">
                                <span>{{setmeal.sex == '0' ? '性别不限' : setmeal.sex == '1' ? '男':'女'}}</span>
                                <span>{{setmeal.age}}</span>
                            </p>
                        </div>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</div>
<!-- 页面 css js -->
<script src="../plugins/vue/vue.js"></script>
<!--<script src="../js/page-health-order.js"></script>-->
<script>
    var vue = new Vue({
        el: '#app',
        data: {
            setmealList: []//套餐列表数据
        },
    });
</script>
</body>