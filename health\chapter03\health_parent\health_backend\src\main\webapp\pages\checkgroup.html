<!DOCTYPE html>
<html>
<head>
    <!-- 页面meta -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>传智健康</title>
    <meta name="description" content="传智健康">
    <meta name="keywords" content="传智健康">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <!-- 引入样式 -->
    <link rel="stylesheet" href="../plugins/elementui/index.css">
    <link rel="stylesheet" href="../plugins/font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <!-- 引入组件库 -->
    <script type="text/javascript" src="../js/jquery.min.js"></script>
    <script src="../js/vue.js"></script>
    <script src="../plugins/elementui/index.js"></script>
    <style>
        .datatable {
            position: relative;
            box-sizing: border-box;
            -webkit-box-flex: 1;
            width: 100%;
            max-width: 100%;
            font-size: 14px;
            color: rgb(96, 98, 102);
            overflow: hidden;
            flex: 1 1 0%;
        }
        .datatable td, .datatable th {
            padding: 12px 0;
            min-width: 0;
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            text-overflow: ellipsis;
            vertical-align: middle;
            position: relative;
            text-align: left;
        }
    </style>
</head>
<body class="hold-transition">
    <div id="app">
        <div class="content-header">
            <h1>预约管理<small>检查组管理</small></h1>
            <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumb">
                <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                <el-breadcrumb-item>预约管理</el-breadcrumb-item>
                <el-breadcrumb-item>检查组管理</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="app-container">
            <div class="box">
                <div class="filter-container">
                    <el-input placeholder="编码/名称/助记码" v-model="pagination.queryString" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter"></el-input>
                    <el-button class="dalfBut" @click="handleCurrentChange(1)">查询</el-button>
                    <el-button type="primary" class="butT" @click="handleCreate()">新增</el-button>
                </div>
                <el-table size="small" current-row-key="id" :data="dataList" stripe highlight-current-row>
                    <el-table-column type="index" align="center" label="序号"></el-table-column>
                    <el-table-column prop="code" label="检查组编码" align="center"></el-table-column>
                    <el-table-column prop="name" label="检查组名称" align="center"></el-table-column>
                    <el-table-column label="适用性别" align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.sex == '0' ? '不限' : scope.row.sex == '1' ? '男' : '女'}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="helpCode" label="助记码" align="center"></el-table-column>
                    <el-table-column prop="remark" label="说明" align="center"></el-table-column>
                    <el-table-column label="操作" align="center">
                        <template slot-scope="scope">
                            <el-button type="primary" size="mini" @click="handleUpdate(scope.row)">编辑</el-button>
                            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination-container">
                    <el-pagination
                        class="pagiantion"
                        @current-change="handleCurrentChange"
                        :current-page="pagination.currentPage"
                        :page-size="pagination.pageSize"
                        :total="pagination.total"
                        layout="total, prev, pager, next, jumper">
                    </el-pagination>
                </div>
                <!-- 新增标签弹层 -->
                <div class="add-form">
                    <el-dialog title="新增检查组" :visible.sync="dialogFormVisible">
                        <template>
                            <el-tabs v-model="activeName" type="card">
                                <el-tab-pane label="基本信息" name="first">
                                    <el-form label-position="right" label-width="100px">
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="编码">
                                                    <el-input v-model="formData.code"/>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="名称">
                                                    <el-input v-model="formData.name"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="适用性别">
                                                    <el-select v-model="formData.sex">
                                                        <el-option label="不限" value="0"></el-option>
                                                        <el-option label="男" value="1"></el-option>
                                                        <el-option label="女" value="2"></el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="助记码">
                                                    <el-input v-model="formData.helpCode"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="说明">
                                                    <el-input v-model="formData.remark" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="注意事项">
                                                    <el-input v-model="formData.attention" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </el-tab-pane>
                                <el-tab-pane label="检查项信息" name="second">
                                <div class="checkScrol">
                                    <table class="datatable">
                                        <thead>
                                        <tr>
                                            <th>选择</th>
                                            <th>项目编码</th>
                                            <th>项目名称</th>
                                            <th>项目说明</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr v-for="c in tableData">
                                            <td>
                                                <input :id="c.id" v-model="checkitemIds" type="checkbox" :value="c.id">
                                            </td>
                                            <td><label :for="c.id">{{c.code}}</label></td>
                                            <td><label :for="c.id">{{c.name}}</label></td>
                                            <td><label :for="c.id">{{c.remark}}</label></td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                </el-tab-pane>
                            </el-tabs>
                        </template>
                        <div slot="footer" class="dialog-footer">
                            <el-button @click="dialogFormVisible = false">取消</el-button>
                            <el-button type="primary" @click="handleAdd()">确定</el-button>
                        </div>
                    </el-dialog>
                </div>

                <!-- 编辑标签弹层 -->
                <div class="edit-form">
                    <el-dialog title="编辑检查组" :visible.sync="dialogFormVisible4Edit">
                        <template>
                            <el-tabs v-model="activeName" type="card">
                                <el-tab-pane label="基本信息" name="first">
                                    <el-form label-position="right" label-width="100px">
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="编码">
                                                    <el-input v-model="formData.code"/>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="名称">
                                                    <el-input v-model="formData.name"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="12">
                                                <el-form-item label="适用性别">
                                                    <el-select v-model="formData.sex">
                                                        <el-option label="不限" value="0"></el-option>
                                                        <el-option label="男" value="1"></el-option>
                                                        <el-option label="女" value="2"></el-option>
                                                    </el-select>
                                                </el-form-item>
                                            </el-col>
                                            <el-col :span="12">
                                                <el-form-item label="助记码">
                                                    <el-input v-model="formData.helpCode"/>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="说明">
                                                    <el-input v-model="formData.remark" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                        <el-row>
                                            <el-col :span="24">
                                                <el-form-item label="注意事项">
                                                    <el-input v-model="formData.attention" type="textarea"></el-input>
                                                </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </el-tab-pane>
                                <el-tab-pane label="检查项信息" name="second">
                                    <div class="checkScrol">
                                        <table class="datatable">
                                            <thead>
                                                <tr>
                                                    <th>选择</th>
                                                    <th>项目编码</th>
                                                    <th>项目名称</th>
                                                    <th>项目说明</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="c in tableData">
                                                    <td>
                                                        <input :id="c.id" v-model="checkitemIds" type="checkbox" :value="c.id">
                                                    </td>
                                                    <td><label :for="c.id">{{c.code}}</label></td>
                                                    <td><label :for="c.id">{{c.name}}</label></td>
                                                    <td><label :for="c.id">{{c.remark}}</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </el-tab-pane>
                            </el-tabs>
                        </template>
                        <div slot="footer" class="dialog-footer">
                            <el-button @click="dialogFormVisible4Edit = false">取消</el-button>
                            <el-button type="primary" @click="handleEdit()">确定</el-button>
                        </div>
                    </el-dialog>
                </div>
            </div>
        </div>
    </div>
</body>
<script src="../js/axios-0.18.0.js"></script>
<script>
    var vue = new Vue({
        el: '#app',
        data:{
            activeName:'first',//添加/编辑窗口Tab标签名称
            pagination: {//分页相关属性
                currentPage:1,    //当前页码
                pageSize:10,       //每页显示的记录数
                total:0,            //总记录数
                queryString:null  //查询条件
            },
            dataList: [],//列表数据
            formData: {},//表单数据
            tableData:[],//新增和编辑表单中对应的检查项列表数据
            checkitemIds:[],//新增和编辑表单中检查项对应的复选框，基于双向绑定可以进行回显和数据提交
            dialogFormVisible: false,//控制添加窗口显示/隐藏
            dialogFormVisible4Edit:false//控制编辑窗口显示/隐藏
        },
        //钩子函数，VUE对象初始化完成后自动执行
        created() {
            this.findPage();//调用分页查询方法完成分页查询
        },
        methods:{
            //重置表单
            resetForm(){
                this.formData = {};            //清空表单域
                this.activeName = 'first';   //默认显示基本信息区
                this.checkitemIds = [];       //清空勾选的检查项
            },
            //弹出新增检查组对话框
            handleCreate() {
                this.resetForm();                //调用重置表单的方法
                this.dialogFormVisible = true;//修改显示弹窗的属性为true
                //发送Ajax请求，查询所有的检查项信息，以表格的形式展示到对话框中
                axios.get("/checkitem/findAll.do").then((res) => {
                    if(res.data.flag){
                        //查询成功，为tableData赋值
                        this.tableData = res.data.data;
                    }else{
                        this.$message.error(res.data.message);
                    }
                });
            },
            //添加检查组
            handleAdd() {
                //发送Ajax请求，需要提交检查组基本信息和勾选的检查项信息
                axios.post("/checkgroup/add.do?checkitemIds=" + this.checkitemIds,this.formData).then((res) => {
                    if(res.data.flag){
                        this.dialogFormVisible = false;//成功,关闭新增对话框
                        //弹出请求成功提示信息
                        this.$message({
                            type:'success',
                            message:res.data.message
                        });
                        this.findPage();//分页查询
                    }else{
                        //执行失败，弹出提示信息
                        this.$message.error(res.data.message);
                    }
                });
            },
            //分页查询
            findPage(){
                //定义分页参数
                var param = {
                    currentPage:this.pagination.currentPage,//当前页
                    pageSize:this.pagination.pageSize,       //每页显示记录数
                    queryString:this.pagination.queryString //查询条件
                };
                //发送Ajax请求，进行分页查询
                axios.post("/checkgroup/findPage.do",param).then((res)=> {
                    this.dataList = res.data.rows;           //查询结果
                    this.pagination.total = res.data.total;//总记录数
                });
            },
            //切换页码
            handleCurrentChange(currentPage) {
                this.pagination.currentPage = currentPage;//指定最新的页码
                this.findPage();//调用分页方法
            },
            //弹出编辑检查组对话框
            handleUpdate(row) {
                this.activeName = 'first';//每次弹出的编辑对话框默认显示基本信息区
                this.dialogFormVisible4Edit = true;//编辑对话框显示
                //基本信息回显，发送Ajax请求查询检查组基本信息
                axios.get("/checkgroup/findById.do?id=" + row.id).then((res) => {
                    if(res.data.flag){
                        //为模型数据赋值，基于Vue数据绑定进行回显
                        this.formData = res.data.data;
                    }
                });
                //发送Ajax请求，加载检查项列表，以表格的形式展示到对话框中
                axios.get("/checkitem/findAll.do").then((res) => {
                    if (res.data.flag){
                        //查询检查项成功，为tableData赋值
                        this.tableData = res.data.data;
                        //查询检查组中包含的检查项
                        axios.get("/checkgroup/" +
                            "findCheckItemIdsByCheckGroupId.do?" +
                            "checkgroupId=" + row.id).then((res) => {
                            if (res.data.flag){
                                //查询成功，为checkitemIds赋值
                                this.checkitemIds = res.data.data;
                            }
                        });
                    }else{
                        //查询检查项失败，返回错误提示信息
                        this.$message.error(res.data.message);
                    }
                });
            },
            //编辑检查组
            handleEdit(){
                axios.post("/checkgroup/edit.do?checkitemIds="+ this.checkitemIds,this.formData).then((res) => {
                    if (res.data.flag){
                        this.dialogFormVisible4Edit = false;//关闭编辑对话框
                        //弹出提示信息
                        this.$message({
                            type:'success',
                            message:res.data.message
                        });
                        this.findPage();//执行分页查询
                    }else{
                        //执行失败，弹出提示信息
                        this.$message.error(res.data.message);
                    }
                });
            },
            //删除检查组
            handleDelete(row) {
                //弹出确定删除提示对话框
                this.$confirm('你确定要删除当前数据吗？','提示',{
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    //发送Ajax请求，把要删除的id提交到Controller
                    axios.get("/checkgroup/delete.do?id=" + row.id).then((res) => {
                        if (res.data.flag){
                            //处理成功
                            this.$message({
                                type:'success',
                                message: res.data.message
                            });
                            this.findPage();//调用分页查询
                        }else{
                            this.$message.error(res.data.message);//处理失败
                        }
                    });
                }).catch(() => {
                    this.$message("已取消");
                })
            }
        }
    })
</script>
</html>
