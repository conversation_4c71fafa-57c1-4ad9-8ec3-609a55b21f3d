/*
 * colors variables
 */
.carousel {
  width: 100%;
  max-width: 750px;
  position: relative;
}

.carousel .banner-roll {
  width: 100%;
  height: 100%;
  position: relative;
}

.carousel .banner-roll .indicators {
  position: relative;
  display: inline-block;
  z-index: 51;
  left: 50%;
  transform: translateX(-50%);
  bottom: 30px;
}

.carousel .banner-roll .indicators span {
  display: inline-block;
  width: 6px;
  height: 6px;
  margin: 5px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.carousel .banner-roll .indicators span.active {
  background: #fff;
}

.carousel .banner-roll .banner-item {
  overflow: hidden;
  position: relative;
  height: 100%;
}

.carousel .banner-roll .banner-item .item {
  position: absolute;
  background: top center no-repeat;
  background-size: contain;
  width: 100%;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
}
