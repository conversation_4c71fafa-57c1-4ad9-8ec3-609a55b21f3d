-- 健康管理系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS health DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE health;

-- 1. 检查项表
CREATE TABLE IF NOT EXISTS t_checkitem (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(16) UNIQUE NOT NULL COMMENT '项目编码',
    name VARCHAR(32) NOT NULL COMMENT '项目名称',
    sex CHAR(1) COMMENT '适用性别：0不限 1男 2女',
    age VARCHAR(32) COMMENT '适用年龄',
    price FLOAT COMMENT '价格',
    type CHAR(1) COMMENT '检查项类型',
    remark VARCHAR(128) COMMENT '项目说明',
    attention VARCHAR(128) COMMENT '注意事项'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查项表';

-- 2. 检查组表
CREATE TABLE IF NOT EXISTS t_checkgroup (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(32) UNIQUE NOT NULL COMMENT '编码',
    name VARCHAR(32) NOT NULL COMMENT '名称',
    helpCode VARCHAR(32) COMMENT '助记码',
    sex CHAR(1) COMMENT '适用性别：0不限 1男 2女',
    remark VARCHAR(128) COMMENT '说明',
    attention VARCHAR(128) COMMENT '注意事项'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查组表';

-- 3. 套餐表
CREATE TABLE IF NOT EXISTS t_setmeal (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(128) NOT NULL COMMENT '套餐名称',
    code VARCHAR(8) UNIQUE NOT NULL COMMENT '套餐编码',
    helpCode VARCHAR(32) COMMENT '助记码',
    sex CHAR(1) COMMENT '适用性别：0不限 1男 2女',
    age VARCHAR(32) COMMENT '适用年龄',
    price FLOAT COMMENT '套餐价格',
    remark VARCHAR(200) COMMENT '套餐说明',
    attention VARCHAR(200) COMMENT '注意事项',
    img VARCHAR(128) COMMENT '套餐对应图片存储路径'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查套餐表';

-- 4. 检查组和检查项关联表（多对多）
CREATE TABLE IF NOT EXISTS t_checkgroup_checkitem (
    checkgroup_id INT NOT NULL COMMENT '检查组id',
    checkitem_id INT NOT NULL COMMENT '检查项id',
    PRIMARY KEY (checkgroup_id, checkitem_id),
    FOREIGN KEY (checkgroup_id) REFERENCES t_checkgroup(id) ON DELETE CASCADE,
    FOREIGN KEY (checkitem_id) REFERENCES t_checkitem(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='检查组检查项关联表';

-- 5. 套餐和检查组关联表（多对多）
CREATE TABLE IF NOT EXISTS t_setmeal_checkgroup (
    setmeal_id INT NOT NULL COMMENT '套餐id',
    checkgroup_id INT NOT NULL COMMENT '检查组id',
    PRIMARY KEY (setmeal_id, checkgroup_id),
    FOREIGN KEY (setmeal_id) REFERENCES t_setmeal(id) ON DELETE CASCADE,
    FOREIGN KEY (checkgroup_id) REFERENCES t_checkgroup(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套餐检查组关联表';

-- 6. 用户表
CREATE TABLE IF NOT EXISTS t_user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    birthday DATE COMMENT '生日',
    gender VARCHAR(32) COMMENT '性别',
    username VARCHAR(32) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(256) NOT NULL COMMENT '密码',
    remark VARCHAR(32) COMMENT '备注',
    station VARCHAR(128) COMMENT '状态',
    telephone VARCHAR(32) COMMENT '手机号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 7. 会员表
CREATE TABLE IF NOT EXISTS t_member (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fileNumber VARCHAR(32) COMMENT '档案号',
    name VARCHAR(32) COMMENT '姓名',
    sex VARCHAR(32) COMMENT '性别',
    idCard VARCHAR(18) COMMENT '身份证号',
    phoneNumber VARCHAR(32) COMMENT '手机号',
    regTime DATE COMMENT '注册时间',
    password VARCHAR(32) COMMENT '密码',
    email VARCHAR(32) COMMENT '邮箱',
    birthday DATE COMMENT '生日',
    remark VARCHAR(128) COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员表';

-- 8. 预约表
CREATE TABLE IF NOT EXISTS t_order (
    id INT AUTO_INCREMENT PRIMARY KEY,
    member_id INT COMMENT '会员id',
    orderDate DATE COMMENT '预约日期',
    orderType VARCHAR(8) COMMENT '预约类型',
    orderStatus VARCHAR(8) COMMENT '预约状态',
    setmeal_id INT COMMENT '套餐id',
    FOREIGN KEY (member_id) REFERENCES t_member(id),
    FOREIGN KEY (setmeal_id) REFERENCES t_setmeal(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预约表';

-- 9. 预约设置表
CREATE TABLE IF NOT EXISTS t_ordersetting (
    id INT AUTO_INCREMENT PRIMARY KEY,
    orderDate DATE UNIQUE NOT NULL COMMENT '预约设置日期',
    number INT NOT NULL COMMENT '可预约人数',
    reservations INT DEFAULT 0 COMMENT '已预约人数'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预约设置表';

-- 插入初始数据
-- 插入管理员用户
INSERT INTO t_user (username, password, remark) VALUES 
('admin', 'admin', '系统管理员') 
ON DUPLICATE KEY UPDATE password='admin';

-- 插入示例检查项
INSERT INTO t_checkitem (code, name, sex, age, price, type, remark, attention) VALUES 
('0001', '身高', '0', '0-100', 5.0, '1', '身高测量', '请脱鞋测量'),
('0002', '体重', '0', '0-100', 5.0, '1', '体重测量', '请空腹测量'),
('0003', '血压', '0', '18-100', 20.0, '1', '血压检查', '测量前请休息5分钟'),
('0004', '血常规', '0', '0-100', 25.0, '2', '血液常规检查', '需要空腹8小时以上')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 插入示例检查组
INSERT INTO t_checkgroup (code, name, helpCode, sex, remark, attention) VALUES 
('0001', '一般检查', 'YBJC', '0', '基础体检项目', '请按时到达'),
('0002', '血液检查', 'XYJC', '0', '血液相关检查', '需要空腹')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 插入示例套餐
INSERT INTO t_setmeal (name, code, helpCode, sex, age, price, remark, attention, img) VALUES 
('入职无忧体检套餐（男女通用）', '0001', 'RZTC', '0', '18-60', 300.0, '适合入职体检', '体检前一天晚上8点后禁食', 'setmeal1.jpg'),
('阳光爸妈升级肿瘤12项筛查（男女单人）', '0002', 'YGBM', '0', '55-100', 1200.0, '中老年体检套餐', '体检前需要预约', 'setmeal2.jpg')
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- 建立关联关系
INSERT IGNORE INTO t_checkgroup_checkitem (checkgroup_id, checkitem_id) VALUES 
(1, 1), (1, 2), (1, 3), (2, 4);

INSERT IGNORE INTO t_setmeal_checkgroup (setmeal_id, checkgroup_id) VALUES 
(1, 1), (1, 2), (2, 1), (2, 2);

-- 插入预约设置示例数据（未来30天）
INSERT IGNORE INTO t_ordersetting (orderDate, number, reservations) 
SELECT DATE_ADD(CURDATE(), INTERVAL seq DAY), 100, 0
FROM (
    SELECT 0 seq UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION 
    SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9 UNION 
    SELECT 10 UNION SELECT 11 UNION SELECT 12 UNION SELECT 13 UNION SELECT 14 UNION 
    SELECT 15 UNION SELECT 16 UNION SELECT 17 UNION SELECT 18 UNION SELECT 19 UNION 
    SELECT 20 UNION SELECT 21 UNION SELECT 22 UNION SELECT 23 UNION SELECT 24 UNION 
    SELECT 25 UNION SELECT 26 UNION SELECT 27 UNION SELECT 28 UNION SELECT 29
) AS seq_table;

COMMIT;

-- 显示创建结果
SELECT 'Database initialization completed successfully!' AS status;
SHOW TABLES;
