<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="FindBugsConfigurable">
    <option name="make" value="true" />
    <option name="effort" value="default" />
    <option name="priority" value="Medium" />
    <option name="excludeFilter" value="" />
  </component>
  <component name="FrameworkDetectionExcludesConfiguration">
    <file type="web" url="file://$PROJECT_DIR$/health_backend" />
    <file type="web" url="file://$PROJECT_DIR$/health_mobile" />
    <file type="web" url="file://$PROJECT_DIR$/health_service_provider" />
  </component>
  <component name="JavaScriptSettings">
    <option name="languageLevel" value="ES6" />
  </component>
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
  <component name="SuppressionsComponent">
    <option name="suppComments" value="[]" />
  </component>
</project>