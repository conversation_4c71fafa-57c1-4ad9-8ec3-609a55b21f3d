<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cc12c23a-d298-4eef-8210-27603af469d1" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../../../Program Files/apache-maven-3.6.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="$PROJECT_DIR$/../../../Program Files/apache-maven-3.6.3\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="21kuoxRQ0t0Vt3ua0nZ5MUmuG2Y" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.health_mobile [tomcat7:run].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/health/health/chapter08/health_parent/pom.xml&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.itheima.pojo" />
      <recent name="com.itheima.utils" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\a-czjk\health_parent\health_service_provider\src\main\webapp\WEB-INF" />
      <recent name="D:\a-czjk\health_parent\health_common\src\main\java\com\itheima\utils" />
      <recent name="D:\a-czjk\health_parent\health_mobile\src\main\resources" />
      <recent name="D:\a-czjk\health_parent\health_common\src\main\java\com\itheima\constant" />
      <recent name="D:\a-czjk\health_parent\health_common\src\main\java\com\itheima\pojo" />
    </key>
  </component>
  <component name="RunManager" selected="Maven.health_backend [tomcat7:run]">
    <configuration name="health_backend [tomcat7:run]" type="MavenRunConfiguration" factoryName="Maven" temporary="true">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list>
                <option value="tomcat7:run" />
              </list>
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" value="pom.xml" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="$PROJECT_DIR$/health_backend" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
    <configuration name="health_mobile [tomcat7:run]" type="MavenRunConfiguration" factoryName="Maven" temporary="true">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list>
                <option value="tomcat7:run" />
              </list>
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" value="pom.xml" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="$PROJECT_DIR$/health_mobile" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
    <configuration name="health_service_provider [tomcat7:run]" type="MavenRunConfiguration" factoryName="Maven" temporary="true">
      <MavenSettings>
        <option name="myGeneralSettings" />
        <option name="myRunnerSettings" />
        <option name="myRunnerParameters">
          <MavenRunnerParameters>
            <option name="cmdOptions" />
            <option name="profiles">
              <set />
            </option>
            <option name="goals">
              <list>
                <option value="tomcat7:run" />
              </list>
            </option>
            <option name="multimoduleDir" />
            <option name="pomFileName" value="pom.xml" />
            <option name="profilesMap">
              <map />
            </option>
            <option name="projectsCmdOptionValues">
              <list />
            </option>
            <option name="resolveToWorkspace" value="false" />
            <option name="workingDirPath" value="$PROJECT_DIR$/health_service_provider" />
          </MavenRunnerParameters>
        </option>
      </MavenSettings>
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Maven.health_backend [tomcat7:run]" />
      <item itemvalue="Maven.health_mobile [tomcat7:run]" />
      <item itemvalue="Maven.health_service_provider [tomcat7:run]" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Maven.health_mobile [tomcat7:run]" />
        <item itemvalue="Maven.health_backend [tomcat7:run]" />
        <item itemvalue="Maven.health_service_provider [tomcat7:run]" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="cc12c23a-d298-4eef-8210-27603af469d1" name="Default Changelist" comment="" />
      <created>1638497584868</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1638497584868</updated>
      <workItem from="1638497587395" duration="9470000" />
      <workItem from="1638511268004" duration="9534000" />
      <workItem from="1640567716120" duration="8487000" />
      <workItem from="1640653918790" duration="9000000" />
      <workItem from="1640739865213" duration="876000" />
      <workItem from="1640755544297" duration="7146000" />
      <workItem from="1640826189438" duration="5259000" />
      <workItem from="1647574349174" duration="4165000" />
      <workItem from="1647586750716" duration="3000" />
      <workItem from="1675308177325" duration="11613000" />
      <workItem from="1758954598998" duration="220000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>