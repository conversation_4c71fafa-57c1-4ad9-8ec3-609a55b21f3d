/*
 * colors variables
 */
/*
 * colors variables
 */
html, body {
  min-height: 100%;
  height: 100%;
  background: #f3f2f7;
  line-height: 2;
  font-family: Helvetica;
}

html, body, h1, h2, h3, h4, h5, h6, p, ul, li {
  margin: 0;
  padding: 0;
}

ul, li {
  list-style: none;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: normal;
}

h3 {
  font-size: 16px;
}

h4 {
  font-size: 14px;
}

p {
  font-size: 12px;
}

em, i {
  font-style: normal;
}

ul {
  list-style: none;
}

a {
  text-decoration: none;
}

a:hover {
  -webkit-tap-highlight-color: transparent;
}

.link-page {
  display: block;
}

img {
  width: 100%;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.center {
  text-align: center;
}

.img-parent {
  overflow: hidden;
}

:focus {
  outline: none;
}

input::-webkit-input-placeholder {
  /* WebKit browsers */
  color: #d1d1d6;
}

input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  color: #d1d1d6;
}

input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  color: #d1d1d6;
}

input::-ms-input-placeholder {
  /* Internet Explorer 10+ */
  color: #d1d1d6;
}

@font-face {
  font-family: 'health-icon';
  src: url("../fonts/health-icon.eot?l7yt0w");
  src: url("fonts/health-icon.eot?l7yt0w#iefix") format("embedded-opentype"), url("../fonts/health-icon.ttf?l7yt0w") format("truetype"), url("../fonts/health-icon.woff?l7yt0w") format("woff"), url("../fonts/health-icon.svg?l7yt0w") format("svg");
  font-weight: normal;
  font-style: normal;
}

i {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'health-icon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-left-arrow:before {
  content: "\e940";
  color: #515151;
}

.icon-rit-arrow2:before {
  content: "\e941";
  color: #515151;
}

.icon-case .path1:before {
  content: "\e91a";
  color: #48bcf5;
}

.icon-case .path2:before {
  content: "\e91b";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path3:before {
  content: "\e91c";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path4:before {
  content: "\e91d";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path5:before {
  content: "\e91e";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path6:before {
  content: "\e91f";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-case .path7:before {
  content: "\e920";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path8:before {
  content: "\e921";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path9:before {
  content: "\e922";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path10:before {
  content: "\e923";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path11:before {
  content: "\e924";
  margin-left: -1em;
  color: #407afd;
}

.icon-case .path12:before {
  content: "\e925";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path1:before {
  content: "\e926";
  color: #48bcf5;
}

.icon-allergy .path2:before {
  content: "\e927";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path3:before {
  content: "\e928";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path4:before {
  content: "\e929";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path5:before {
  content: "\e92a";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path6:before {
  content: "\e92b";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path7:before {
  content: "\e92c";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path8:before {
  content: "\e92d";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path9:before {
  content: "\e92e";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path10:before {
  content: "\e92f";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path11:before {
  content: "\e930";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path12:before {
  content: "\e931";
  margin-left: -1em;
  color: #407afd;
}

.icon-allergy .path13:before {
  content: "\e932";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-allergy .path14:before {
  content: "\e933";
  margin-left: -1em;
  color: #407afd;
}

.icon-baseinfo .path1:before {
  content: "\e934";
  color: #48bcf5;
}

.icon-baseinfo .path2:before {
  content: "\e935";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path1:before {
  content: "\e936";
  color: #48bcf5;
}

.icon-family .path2:before {
  content: "\e937";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path3:before {
  content: "\e938";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path4:before {
  content: "\e939";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path5:before {
  content: "\e93a";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path6:before {
  content: "\e93b";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path7:before {
  content: "\e93c";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-family .path8:before {
  content: "\e93d";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path9:before {
  content: "\e93e";
  margin-left: -1em;
  color: #407afd;
}

.icon-family .path10:before {
  content: "\e93f";
  margin-left: -1em;
  color: #407afd;
}

.icon-rit-arrow:before {
  content: "\e919";
}

.icon-back:before {
  content: "\e900";
  color: #fff;
}

.icon-more:before {
  content: "\e901";
  color: #fff;
}

.icon-zhen .path1:before {
  content: "\e902";
  color: #48bcf5;
}

.icon-zhen .path2:before {
  content: "\e904";
  margin-left: -1em;
  color: #4076fe;
}

.icon-search .path1:before {
  content: "\e903";
  color: #48bcf5;
}

.icon-search .path2:before {
  content: "\e905";
  margin-left: -1em;
  color: #4076fe;
}

.icon-search .path3:before {
  content: "\e917";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-search .path4:before {
  content: "\e918";
  margin-left: -1em;
  color: #4076fe;
}

.icon-record .path1:before {
  content: "\e906";
  color: #48bcf5;
}

.icon-record .path2:before {
  content: "\e907";
  margin-left: -1em;
  color: #4076fe;
}

.icon-record .path3:before {
  content: "\e908";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-record .path4:before {
  content: "\e909";
  margin-left: -1em;
  color: #48bcf5;
}

.icon-record .path5:before {
  content: "\e90a";
  margin-left: -1em;
  color: #4076fe;
}

.icon-star .path1:before {
  content: "\e90b";
  color: #48bcf5;
}

.icon-star .path2:before {
  content: "\e90c";
  margin-left: -1em;
  color: #4076fe;
}

.icon-plus .path1:before {
  content: "\e90d";
  color: #48bcf5;
}

.icon-plus .path2:before {
  content: "\e90e";
  margin-left: -1em;
  color: #4076fd;
}

.icon-person .path1:before {
  content: "\e90f";
  color: #48bcf5;
}

.icon-person .path2:before {
  content: "\e910";
  margin-left: -1em;
  color: #4076fe;
}

.icon-date:before {
  content: "\e911";
  color: #4b88fc;
}

.icon-data:before {
  content: "\e912";
  color: #fff;
}

.icon-ask-cur:before {
  content: "\e913";
  color: #fff;
}

.icon-ask-circle .path1:before {
  content: "\e914";
  color: #48bcf5;
}

.icon-ask-circle .path2:before {
  content: "\e915";
  margin-left: -1em;
  color: #4076fe;
}

.icon-tizhi:before {
  content: "\e916";
  color: #fff;
}

.app {
  max-width: 750px;
  margin: 0 auto;
}

/*
 * colors variables
 */
.top-header {
  background-color: #000;
  color: #fff;
  text-align: center;
  height: 45px;
  line-height: 45px;
  font-size: 16px;
  padding: 0 10px;
}

/*
 * colors variables
 */
.login {
  height: 100%;
  background-color: #fff;
}

.login input {
  font-size: 18px;
}

.login :focus {
  outline: none;
}

.login input:focus {
  -webkit-tap-highlight-color: transparent;
  -webkit-user-modify: read-write-plaintext-only;
}

.login input::-webkit-input-placeholder {
  /* WebKit browsers */
  font-size: 15px;
}

.login input:-moz-placeholder {
  /* Mozilla Firefox 4 to 18 */
  font-size: 15px;
}

.login input::-moz-placeholder {
  /* Mozilla Firefox 19+ */
  font-size: 15px;
}

.login input::-ms-input-placeholder {
  /* Internet Explorer 10+ */
  font-size: 18px;
}

.login #login-form {
  padding: 4% 5.3%;
}

.login #login-form label {
  float: left;
  padding: 0;
  font-size: 15px;
  letter-spacing: 2px;
  color: #333;
  width: 80px;
  line-height: 2;
  font-family: 'Helvetica Neue',Helvetica,sans-serif;
}
.loginInput{
  margin-left: 90px;
  min-width: 230px;
}
.loginInput #account{
  width: 45%;
}
.login #login-form .input-row {
  clear: left;
  overflow: hidden;
  position: relative;
  height: auto;
  margin-bottom: 2.7%;
  border-bottom: 1px solid #f3f2f7;
}

.login #login-form .input-row label ~ input {
  height: auto;
  line-height: 1.5;
  float: left;
  margin-bottom: 0;
  padding-left: 0;
  border: 0;
}

.login #login-form .btn {
  font-weight: 400;
  line-height: 1.42;
  display: block;
  margin-bottom: 0;
  margin-top: 4.7%;
  cursor: pointer;
  -webkit-transition: all;
  transition: all;
  -webkit-transition-timing-function: linear;
  transition-timing-function: linear;
  -webkit-transition-duration: .2s;
  transition-duration: .2s;
  text-align: center;
  white-space: nowrap;
  border-radius: 3px;
  background-clip: padding-box;
  font-size: 20px;
  letter-spacing: 4px;
}

.login #login-form .btn a {
  color: #fff;
}

.login #login-form .btn.yes-btn, .login #login-form .btn.cancel-btn {
  padding-top: 2.5%;
  padding-bottom: 2.5%;
  color: #fff;
}

.login #login-form .btn.cancel-btn {
  background-image: linear-gradient(90deg, #7cc8f6, #57b3ec);
}

.login #login-form .btn.yes-btn {
  background-image: linear-gradient(90deg, #26acff, #0088dc);
}
