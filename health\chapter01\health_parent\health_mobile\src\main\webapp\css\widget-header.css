/*
 * colors variables
 */
header {
  width: 100%;
  max-width: 640px;
  position: relative;
}

header .banner-roll {
  width: 100%;
  height: 100%;
  position: relative;
}

header .banner-roll .indicators {
  position: relative;
  display: inline-block;
  z-index: 51;
  left: 50%;
  transform: translateX(-50%);
  bottom: 30px;
}

header .banner-roll .indicators span {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 5px;
  border: solid 2px #fff;
  border-radius: 50%;
}

header .banner-roll .active {
  background: #fff;
}

header .banner-roll .banner-item {
  overflow: hidden;
  position: relative;
  height: 100%;
}

header .banner-roll .banner-item .item {
  position: absolute;
  background: top center no-repeat;
  background-size: contain;
  width: 100%;
  top: 0px;
  left: 50%;
  transform: translateX(-50%);
}

header .searchBox {
  position: absolute;
  height: 40px;
  z-index: 99;
  width: 100%;
  padding: 0 5%;
  top: 10%;
}

header .searchBox .search {
  width: 85%;
  float: left;
  height: 40px;
  background: #333;
  box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

header .searchBox .search input {
  line-height: 38px;
  border-radius: 3px;
  width: 100%;
  padding: 0 10px;
  border: none;
}

header .searchBox .searchMap {
  width: 15%;
  text-align: right;
  float: right;
  font-size: 32px;
}

header .searchBox .searchMap i:before {
  color: #333;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}
