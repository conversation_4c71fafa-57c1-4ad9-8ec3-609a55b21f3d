/*
 * colors variables
 */
.text-link {
  padding: 0;
}

.text-link .list-box {
  padding: 2% 0 0 2%;
  display: flex;
  flex-wrap: wrap;
}

.text-link .list-box li {
  list-style-type: none;
  width: 48%;
  height: 200px;
  margin-bottom: 10px;
  margin-right: 2%;
  background-color: #fff;
  display: table;
  text-align: center;
}

.text-link .list-box li a {
  display: table-cell;
  vertical-align: middle;
}

.text-link .list-box li a:focus, .text-link .list-box li a:visited {
  background-color: transparent;
}

.text-link .list-box li a i {
  display: block;
  font-size: 36px;
}

.text-link .list-box li a span {
  color: #333;
}
