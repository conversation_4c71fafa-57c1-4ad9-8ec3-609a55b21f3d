<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/mvc
						http://www.springframework.org/schema/mvc/spring-mvc.xsd
						http://code.alibabatech.com/schema/dubbo
						http://code.alibabatech.com/schema/dubbo/dubbo.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context.xsd">
    <!--开启spring注解使用，Autowired、Controller、Service、Repository等-->
    <context:annotation-config></context:annotation-config>

    <!--注册自定义job-->
    <bean id="myJob" class="com.itheima.jobs.ClearImgJob"></bean>

    <!--jobdetail，作用是通过反射调用真正的job方法-->
    <bean id="jobDetail" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
        <!--目标对象-->
        <property name="targetObject" ref="myJob"></property>
        <!--目标方法-->
        <property name="targetMethod" value="clearImg"></property>
    </bean>

    <!--配置触发器，指定触发的时间-->
    <bean id="myTrigger" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
        <property name="jobDetail" ref="jobDetail"></property>
        <!--通过cron表达式指定触发的时间-->
        <property name="cronExpression">
            <value>0/15 * * * * ?</value>
        </property>
    </bean>

    <!--配置统一的调度工厂，调度多个任务-->
    <bean class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
        <property name="triggers">
            <list>
                <ref bean="myTrigger"></ref>
            </list>
        </property>
    </bean>
</beans>