.mt_body{
	height: 240px;
	overflow: hidden;
	position: relative;
}
.mt_panel{
	background-color: #F7F7F7;
	padding: 10px 0;
}
.mt_panel h3{
	color: #4eccc4;
	font-size: 16px;
	text-align: center;
	font-weight: normal;
}
.mt_date, .mt_time{
	float: left;
	width: 50%;
	height: 100%;
	vertical-align: top;
	text-align: center;
}
.mt_body ul li{
	height: 48px;
	line-height: 48px;
	font-size: 18px;
	color: #555;
}
.mt_indicate, .mt_indicate:after{
	top: 95px;
	position: absolute;
	border-top: 1px solid #e9e9e9;
	width: 100%;
	height: 0;
	font-size: 0;
}
.mt_indicate:after{
	content: "";
    top: 48px;
}
.mt_body li.selected{
	color: #4eccc4;
}
.mt_body ul .mt_note{
	font-size: 12px;
	color: #000;
}
.mt_confirm{
	text-align: center;
	margin-top: 10%;
	margin-bottom: 10%;
}
.mt_ok{
	color: #4eccc4;
}
.mt_cancel{
	color: #4eccc4;
	margin-left: 16%;
}
.mt_mask{
	width: 100%;
    height: 100%;
    -moz-transition: opacity .5s linear 0s;
    -webkit-transition: opacity .5s linear 0s;
    -o-transition: opacity .5s linear 0s;
    -ms-transition: opacity .5s linear 0s;
    transition: opacity .5s linear 0s;
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    visibility: hidden;
    background: #000;
    opacity: 0;
    z-index: 10;
}
.mt_mask.show{
	visibility: visible;
	opacity: 0.25;
}
.mt_poppanel {
    -moz-transition: -moz-transform .3s ease-in-out 0s;
    -ms-transition: -ms-transform .3s ease-in-out 0s;
    -webkit-transition: -webkit-transform .3s ease-in-out 0s;
    -o-transition: -o-transform .3s ease-in-out 0s;
    transition: transform .3s ease-in-out 0s;
    -ms-transform: translate3d(0,100%,0);
    -moz-transform: translate3d(0,100%,0);
    -webkit-transform: translate3d(0,100%,0);
    -o-transform: translate3d(0,100%,0);
    transform: translate3d(0,100%,0);
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: auto;
    z-index: 11;
    -webkit-user-select : none;
}
.mt_poppanel.show{
	-ms-transform: translate3d(0,0,0);
	-moz-transform: translate3d(0,0,0);
	-webkit-transform: translate3d(0,0,0);
	-o-transform: translate3d(0,0,0);
	transform: translate3d(0,0,0);
}