<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- 上述3个meta标签*必须*放在最前面，任何其他内容都*必须*跟随其后！ -->
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,user-scalable=no,minimal-ui">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../img/asset-favico.ico">
    <title>登录</title>
    <link rel="stylesheet" href="../css/page-health-login.css"/>
    <link rel="stylesheet" href="../plugins/elementui/index.css"/>
    <script src="../plugins/jquery/dist/jquery.min.js"></script>
    <script src="../plugins/vue/vue.js"></script>
    <script src="../plugins/elementui/index.js"></script>
</head>
<body data-spy="scroll" data-target="#myNavbar" data-offset="150">
<div class="app" id="app">
    <!-- 页面头部 -->
    <div class="top-header">
        <span class="f-left"><i class="icon-back"></i></span>
        <span class="center">传智健康</span>
        <span class="f-right"><i class="icon-more"></i></span>
    </div>
    <div style="margin-left: 20px">手机快速登录</div>
    <!-- 页面内容 -->
    <div class="contentBox">
        <div class="login">
            <form id='login-form'>
                <div class="input-row">
                    <label>手机号</label>
                    <div class="loginInput">
                        <input v-model="loginInfo.telephone" id='account' type="text" placeholder="请输入手机号">
                        <input id="validateCodeButton" type="button" style="font-size: 12px" value="获取验证码">
                    </div>
                </div>
                <div class="input-row">
                    <label>验证码</label>
                    <div class="loginInput">
                        <input v-model="loginInfo.validateCode" style="width:80%" id='password' type="password"
                               placeholder="请输入验证码">
                    </div>
                </div>
                <div class="input-row" style="font-size: 12px">
                    <input type="checkbox" checked>
                    阅读并同意《传智健康用户协议》《传智健康隐私条款》
                </div>
                <div class="btn yes-btn"><a href="#">登录</a></div>
            </form>
        </div>
    </div>
</div>
</body>
<script>
    var vue = new Vue({
        el: '#app',
        data: {
            loginInfo: {}//登录信息
        },
        methods: {}
    });
</script>
</html>